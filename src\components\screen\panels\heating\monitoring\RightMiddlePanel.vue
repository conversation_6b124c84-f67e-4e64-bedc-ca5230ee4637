<template>
  <PanelBox title="报警趋势分析">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon total"></span>
          <span class="legend-text">总数</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-one"></span>
          <span class="legend-text">一级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-two"></span>
          <span class="legend-text">二级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-three"></span>
          <span class="legend-text">三级报警</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
      <!-- 时间跨度轴 -->
      <div class="time-span-axis" v-if="allData.length > maxVisiblePoints">
        <div class="time-span-container" ref="timeSpanRef">
          <div class="time-span-track">
            <div
              class="time-span-slider"
              :style="sliderStyle"
              @mousedown="startDrag"
            >
              <div class="slider-handle left" @mousedown.stop="startDrag"></div>
              <div class="slider-handle right" @mousedown.stop="startDrag"></div>
            </div>
          </div>
          <div class="time-labels">
            <span class="time-label start">{{ timeSpanLabels.start }}</span>
            <span class="time-label end">{{ timeSpanLabels.end }}</span>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick,computed } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getMonitorAnalysisTrendStatistics } from '@/api/heating'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到 dayIndex
const getDayIndex = (timeRange) => {
  switch (timeRange) {
    case 'week':
      return 7
    case 'month':
      return 30
    case 'year':
      return 365
    default:
      return 7
  }
}

// 图表DOM引用
const chartRef = ref(null)
const timeSpanRef = ref(null)
let chartInstance = null

// 预设颜色
const colors = {
  total: '#055ADB',
  levelOne: '#FF311D',
  levelTwo: '#FF6817',
  levelThree: '#FFD32E'
}

// 时间跨度轴相关
const maxVisiblePoints = 10 // 最大可见数据点数
const allData = ref([]) // 存储所有原始数据
const visibleRange = ref({ start: 0, end: maxVisiblePoints - 1 }) // 当前可见范围
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartRange = ref({ start: 0, end: 0 })

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '总数',
      data: [],
      color: colors.total
    },
    {
      name: '一级报警',
      data: [],
      color: colors.levelOne
    },
    {
      name: '二级报警',
      data: [],
      color: colors.levelTwo
    },
    {
      name: '三级报警',
      data: [],
      color: colors.levelThree
    }
  ]
})

// 计算时间跨度轴的样式
const sliderStyle = computed(() => {
  if (allData.value.length <= maxVisiblePoints) return {}

  const totalWidth = 100 // 百分比
  const startPercent = (visibleRange.value.start / (allData.value.length - maxVisiblePoints)) * totalWidth
  const widthPercent = (maxVisiblePoints / allData.value.length) * totalWidth

  return {
    left: `${startPercent}%`,
    width: `${widthPercent}%`
  }
})

// 计算时间跨度标签
const timeSpanLabels = computed(() => {
  if (allData.value.length === 0) {
    return { start: '', end: '' }
  }

  const startIndex = Math.max(0, visibleRange.value.start)
  const endIndex = Math.min(allData.value.length - 1, visibleRange.value.end)

  return {
    start: allData.value[startIndex]?.date || '',
    end: allData.value[endIndex]?.date || ''
  }
})

// 从接口获取趋势统计数据
const fetchTrendStatistics = async (timeRange) => {
  try {
    const dayIndex = getDayIndex(timeRange)
    console.log('请求趋势数据，dayIndex:', dayIndex)
    const response = await getMonitorAnalysisTrendStatistics(dayIndex)

    console.log('趋势接口返回数据:', response)

    if (response.code === 200 && response.data && response.data.statistics && Array.isArray(response.data.statistics)) {
      const statistics = response.data.statistics
      console.log('处理的趋势统计数据:', statistics)

      // 存储所有原始数据
      allData.value = statistics.map(item => ({
        date: item.date,
        totalCount: item.totalCount || 0,
        level1Count: item.level1Count || 0,
        level2Count: item.level2Count || 0,
        level3Count: item.level3Count || 0
      }))

      // 初始化可见范围
      if (allData.value.length > maxVisiblePoints) {
        visibleRange.value = { start: 0, end: maxVisiblePoints - 1 }
      } else {
        visibleRange.value = { start: 0, end: allData.value.length - 1 }
      }

      // 更新图表显示
      updateVisibleData()
    } else {
      console.log('接口返回数据格式异常，生成默认数据')
      allData.value = []
      generateDefaultData(timeRange)
      updateChart()
    }
  } catch (error) {
    console.error('获取趋势统计数据失败:', error)
    allData.value = []
    generateDefaultData(timeRange)
    updateChart()
  }
}

// 更新可见数据到图表
const updateVisibleData = () => {
  if (allData.value.length === 0) return

  const startIndex = Math.max(0, visibleRange.value.start)
  const endIndex = Math.min(allData.value.length - 1, visibleRange.value.end)

  const visibleData = allData.value.slice(startIndex, endIndex + 1)

  // 更新图表数据 - X轴直接使用接口返回的日期格式
  chartData.value.xAxis = visibleData.map(item => item.date)
  chartData.value.series[0].data = visibleData.map(item => item.totalCount)
  chartData.value.series[1].data = visibleData.map(item => item.level1Count)
  chartData.value.series[2].data = visibleData.map(item => item.level2Count)
  chartData.value.series[3].data = visibleData.map(item => item.level3Count)

  console.log('更新后的图表数据:', chartData.value)

  // 数据更新后刷新图表
  updateChart()
}

// 时间跨度轴拖拽相关方法
const startDrag = (event) => {
  if (allData.value.length <= maxVisiblePoints) return

  isDragging.value = true
  dragStartX.value = event.clientX
  dragStartRange.value = { ...visibleRange.value }

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const onDrag = (event) => {
  if (!isDragging.value || !timeSpanRef.value) return

  const containerWidth = timeSpanRef.value.offsetWidth
  const deltaX = event.clientX - dragStartX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  // 计算新的范围
  const totalRange = allData.value.length - maxVisiblePoints
  const deltaIndex = Math.round((deltaPercent / 100) * totalRange)

  let newStart = dragStartRange.value.start + deltaIndex
  newStart = Math.max(0, Math.min(newStart, totalRange))

  visibleRange.value = {
    start: newStart,
    end: newStart + maxVisiblePoints - 1
  }

  updateVisibleData()
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 生成默认数据（全为0）
const generateDefaultData = (timeRange) => {
  // 生成一些默认的日期标签
  const now = new Date()
  const defaultData = []

  for (let i = 0; i < 7; i++) {
    const date = new Date(now)
    date.setDate(date.getDate() - (6 - i))
    defaultData.push({
      date: date.toISOString().split('T')[0],
      totalCount: 0,
      level1Count: 0,
      level2Count: 0,
      level3Count: 0
    })
  }

  allData.value = defaultData
  visibleRange.value = { start: 0, end: defaultData.length - 1 }
  updateVisibleData()
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchTrendStatistics(value)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '总数',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.total
        },
        lineStyle: {
          color: colors.total,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '一级报警',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelOne
        },
        lineStyle: {
          color: colors.levelOne,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '二级报警',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelTwo
        },
        lineStyle: {
          color: colors.levelTwo,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '三级报警',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.levelThree
        },
        lineStyle: {
          color: colors.levelThree,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 219, 35, 0.5)' },
              { offset: 1, color: 'rgba(255, 196, 35, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  fetchTrendStatistics(timeRange.value)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)

  // 清理拖拽事件监听器
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.com-select {
  margin-right: 20px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 1px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.level-one {
  background: #FF311D;
}

.legend-icon.level-two {
  background: #FF6817;
}

.legend-icon.level-three {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 时间跨度轴样式 */
.time-span-axis {
  margin-top: 10px;
  padding: 0 20px;
}

.time-span-container {
  position: relative;
  height: 40px;
}

.time-span-track {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.time-span-slider {
  position: absolute;
  top: 0;
  height: 4px;
  background: linear-gradient(90deg, #3AA1FF 0%, #66B8FF 100%);
  border-radius: 2px;
  cursor: grab;
  transition: all 0.1s ease;
}

.time-span-slider:active {
  cursor: grabbing;
}

.slider-handle {
  position: absolute;
  top: -3px;
  width: 10px;
  height: 10px;
  background: #3AA1FF;
  border: 2px solid #FFFFFF;
  border-radius: 50%;
  cursor: grab;
}

.slider-handle:active {
  cursor: grabbing;
}

.slider-handle.left {
  left: -5px;
}

.slider-handle.right {
  right: -5px;
}

.time-labels {
  position: absolute;
  top: 25px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
}

.time-label {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .chart-wrapper {
    min-height: 170px;
  }
}
</style>