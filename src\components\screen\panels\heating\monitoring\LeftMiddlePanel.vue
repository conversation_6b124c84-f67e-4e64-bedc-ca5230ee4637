<template>
  <PanelBox title="视频监控" class="gas-monitoring-left-middle-panel">
    <!-- <template #extra>
      <div class="more-btn" @click="openVideoListModal">
        更多
      </div>
    </template> -->
    <div class="panel-content">
    <!--   <div class="video-grid">
        <div class="video-item" v-for="(video, index) in videoList" :key="index">
          <div class="video-title">{{ video.title }}</div>
          <VideoPlayer 
            :src="video.src" 
            :muted="true" 
            :autoplay="true"
            :showControls="true"
            :ref="el => { if (el) playerRefs[index] = el }"
            class="video-player"
          />
        </div>
      </div> -->
    </div>
  </PanelBox>
   <!-- 视频列表弹窗 -->
   <VideoListModal v-model="showVideoListModal" />
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue'
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';
import VideoListModal from './VideoListModal.vue';

// 播放器引用集合
const playerRefs = ref([]);

// 视频列表数据
const videoList = ref([
{
    title: 'A_富强路桥东北角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000047/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'A_工业路桥西北角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000045/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: '黄河路桥西南角',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000006/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'A_木桥桥西南角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000037/hls.m3u8?originTypeStr=rtp_push'
  }
]);

// 视频列表弹窗显示状态
const showVideoListModal = ref(false);

// "更多"按钮点击事件
const openVideoListModal = () => {
  showVideoListModal.value = true;
};

// 组件卸载时处理
onUnmounted(() => {
  // 销毁播放器，释放资源
  playerRefs.value.forEach(player => {
    if (player && player.player) {
      player.player.dispose();
    }
  });
});
</script>

<style scoped>
.gas-monitoring-left-middle-panel {
 flex: 1;
}
.panel-content {
  height: 100%;
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 视频网格布局 - 2列4行 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  height: 100%;
  width: 100%;
}

.video-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 242, 241, 0.2);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 220px;
  height: 32px;
  background: #000000;
  opacity: 0.4;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 10px;
  color: #FFFFFF;
}

/* 视频播放器响应式调整 */
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 更多按钮样式 */
.more-btn {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 12px;
  color: #3AA1FF;
  cursor: pointer;
  text-decoration: underline;
  margin-right: 30px;
}

.more-btn:hover {
  color: #66B8FF;
}

/* 响应式布局 */
:root {
  --vh-ratio: 1;
}

@media screen and (max-height: 911px) {
  :root {
    --vh-ratio: 0.85;
  }
  
  .video-grid {
    gap: 6px;
  }
  
  .video-title {
    height: 24px;
    font-size: 12px;
    width: 180px;
  }
}

@media screen and (min-height: 1080px) {
  :root {
    --vh-ratio: 1.15;
  }
  
  .video-title {
    height: 36px;
    font-size: 12px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-height: 800px) {
  .video-grid {
    gap: 4px;
  }
  
  .video-title {
    height: 20px;
    width: 160px;
    font-size: 11px;
  }
  
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
}
</style> 